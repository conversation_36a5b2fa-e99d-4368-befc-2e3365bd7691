from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from maternity_center.models import MaternityCenter
from todo_list.enum import TodoListStatusEnum, TodoListTypeEnum
from user.models import Staff
from django.utils import timezone

# 待办事项模型
class TodoList(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name="todo_lists")
    # 指派人
    assign = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="指派人",blank=True,null=True,related_name="assign_todo_lists")
    # 被指派人
    assigned_to = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="被指派人",blank=True,null=True,related_name="assigned_todo_lists")
    # 待办事项类型
    todo_type = models.CharField(max_length=50, verbose_name="待办事项类型", choices=TodoListTypeEnum.choices)
    # 关联入院单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入院单",blank=True,null=True,  related_name="todo_lists")
    # 待办事项内容
    todo_content = models.TextField(verbose_name="待办事项内容")
    # 待办事项备注
    todo_remark = models.TextField(verbose_name="待办事项备注",blank=True,default='')
    # 待办事项状态
    todo_status = models.CharField(max_length=255, verbose_name="待办事项状态",choices=TodoListStatusEnum.choices,blank=True,default=TodoListStatusEnum.PENDING)
    # 完成反馈
    complete_feedback = models.TextField(verbose_name="完成反馈",blank=True,default='')
    # 完成时间
    complete_time = models.DateTimeField(verbose_name="完成时间",blank=True,null=True)
    # rid
    rid = models.CharField(verbose_name="标识符 ID", max_length=100, unique=True,blank=True,default=generate_resource_uuid)


    
    class Meta:
        verbose_name = "待办事项"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.todo_type
    
    def mark_as_completed(self,complete_feedback:str):
        self.todo_status = TodoListStatusEnum.COMPLETED
        self.complete_time = timezone.now()
        self.complete_feedback = complete_feedback
        self.save()
    
    @classmethod
    def get_list_by_maternity_center(cls,maternity_center:MaternityCenter):
        return cls.objects.filter(maternity_center=maternity_center)
    
    @classmethod
    def get_list_by_assign(cls,assign:Staff):
        return cls.objects.filter(assign=assign)
    
    @classmethod
    def get_list_by_assigned_to(cls,assigned_to:Staff):
        return cls.objects.filter(assigned_to=assigned_to)
    
    @classmethod
    def get_assigned_by_rid(cls,rid:str,assigned_to:Staff):
        return cls.objects.get(rid=rid,assigned_to=assigned_to)
    
    @classmethod
    def get_by_rid(cls,rid:str,maternity_center:MaternityCenter):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None
    

    

