from django.urls import path

from .views import (
    HandoverReportDetailView, HandoverReportListView, HandoverReportStaffCreateView, HandoverReportStaffListView, HandoverReportStaffUpdateView, ScheduleDeleteView, ScheduleDetailView, ScheduleListView, ScheduleQueryView, ScheduleUpdateView, StaffContractFileCreateView, StaffContractFileList, StaffContractFileDeleteView,
    StaffCreateView, StaffQualificationCertificateCreateView, StaffUpdateView, StaffResignationView,
    StaffQualificationCertificateList, StaffQualificationCertificateDeleteView, StaffTrainingRecordCreateView,
    StaffTrainingRecordUpdateView, StaffTrainingRecordListView, StaffTrainingRecordDeleteView,
    StaffAssessmentRecordCreateView, StaffAssessmentRecordUpdateView, StaffAssessmentRecordDeleteView,
    StaffAssessmentRecordListView,
    StaffHealthCheckRecordCreateView, StaffHealthCheckRecordUpdateView, StaffHealthCheckRecordDeleteView,
    StaffHealthCheckRecordListView,
    StaffListView, DepartmentListView, ScheduleCreateView, ScheduleValidateView, RoleListView, StaffDetailView
)

urlpatterns = [
    
    # 校验员工排班
    path('schedule/validate/', ScheduleValidateView.as_view(), name='schedule-validate'),
    
    # 创建排班
    path('schedule/create/', ScheduleCreateView.as_view(), name='schedule-create'),
    # 查询排班数据 - 支持多种查询方式
    # ?start_date=2025-01-01&end_date=2025-01-31&staff_sid=xxxxxxxxxxxx&shift_type=MORNING&department_rid=uuidxxxx-xxxx
    path('schedule/query/', ScheduleQueryView.as_view(), name='schedule-query'),
    # 获取排班列表（分页）
    path('schedule/list/', ScheduleListView.as_view(), name='schedule-list'),
    # 排班详情
    path('schedule/detail/<str:rid>/', ScheduleDetailView.as_view(), name='schedule-detail'),
    # 更新排班
    path('schedule/update/<str:rid>/', ScheduleUpdateView.as_view(), name='schedule-update'),
    # 删除排班
    path('schedule/delete/<str:rid>/', ScheduleDeleteView.as_view(), name='schedule-delete'),

    # 交班报告
    # 获取交接班记录列表
    path('handover-report/list/', HandoverReportListView.as_view(), name='handover-list'),
    # 获取交接班记录详情
    path('handover-report/detail/<str:rid>/', HandoverReportDetailView.as_view(), name='handover-detail'),
    # 员工查询交班报告列表
    path('handover-report/staff/list/', HandoverReportStaffListView.as_view(), name='handover-staff-list'),
    # 员工创建交班报告
    path('handover-report/staff/create/<str:rid>/', HandoverReportStaffCreateView.as_view(), name='handover-staff-create'),
    # 员工更新交班报告
    path('handover-report/staff/update/<str:rid>/', HandoverReportStaffUpdateView.as_view(), name='handover-staff-update'),



    # 员工列表
    path('staff/list/', StaffListView.as_view(), name='staff-list'),
    # 员工详情
    path('staff/detail/<str:sid>/', StaffDetailView.as_view(), name='staff-detail'),
    # 员工创建
    path('staff/create/', StaffCreateView.as_view(), name='staff-create'),
    # 员工更新
    path('staff/update/<str:sid>/', StaffUpdateView.as_view(), name='staff-update'),
    # 员工离职
    path('staff/resignation/<str:sid>/', StaffResignationView.as_view(), name='staff-resignation'),
    
    # 获取部门列表
    path('department/list/', DepartmentListView.as_view(), name='department-list'),
    # 获取角色列表
    path('role/list/', RoleListView.as_view(), name='role-list'),

    # 获取指定员工合同列表
    path('staff-contract/list/<str:sid>/', StaffContractFileList.as_view(), name='staff-contract-file-list'),
    # 创建员工合同
    path('staff-contract/create/<str:sid>/', StaffContractFileCreateView.as_view(), name='staff-contract-file-create'),
    # 删除指定合同
    path('staff-contract/delete/<str:rid>/', StaffContractFileDeleteView.as_view(), name='staff-contract-file-delete'),
    
    # 获取指定员工资质证书列表
    path('sqc/list/<str:sid>/', StaffQualificationCertificateList.as_view(), name='staff-qualification-certificate-list'),
    # 创建员工资质证书
    path('sqc/create/<str:sid>/', StaffQualificationCertificateCreateView.as_view(), name='staff-qualification-certificate-create'),
    # 删除指定资质证书
    path('sqc/delete/<str:rid>/', StaffQualificationCertificateDeleteView.as_view(), name='staff-qualification-certificate-delete'),
    
    # 培训记录列表
    path('stra/list/<str:sid>/', StaffTrainingRecordListView.as_view(), name='staff-training-record-list'),
    # 新增员工培训记录  
    path('stra/create/<str:sid>/', StaffTrainingRecordCreateView.as_view(), name='staff-training-record-create'),
    # 更新员工培训记录
    path('stra/update/<str:rid>/', StaffTrainingRecordUpdateView.as_view(), name='staff-training-record-update'),
    # 删除员工培训记录
    path('stra/delete/<str:rid>/', StaffTrainingRecordDeleteView.as_view(), name='staff-training-record-delete'),
    
    
    # 考核记录列表
    path('sar/list/<str:sid>/', StaffAssessmentRecordListView.as_view(), name='staff-assessment-record-list'),
    # 新增员工考核记录  
    path('sar/create/<str:sid>/', StaffAssessmentRecordCreateView.as_view(), name='staff-assessment-record-create'),
    # 更新员工考核记录
    path('sar/update/<str:rid>/', StaffAssessmentRecordUpdateView.as_view(), name='staff-assessment-record-update'),
    # 删除员工考核记录
    path('sar/delete/<str:rid>/', StaffAssessmentRecordDeleteView.as_view(), name='staff-assessment-record-delete'),
    
    
    # 健康检查记录列表
    path('shr/list/<str:sid>/', StaffHealthCheckRecordListView.as_view(), name='staff-health-check-record-list'),
    # 新增员工健康检查记录  
    path('shr/create/<str:sid>/', StaffHealthCheckRecordCreateView.as_view(), name='staff-health-check-record-create'),
    # 更新员工健康检查记录
    path('shr/update/<str:rid>/', StaffHealthCheckRecordUpdateView.as_view(), name='staff-health-check-record-update'),
    # 删除员工健康检查记录
    path('shr/delete/<str:rid>/', StaffHealthCheckRecordDeleteView.as_view(), name='staff-health-check-record-delete'),
    
    
] 